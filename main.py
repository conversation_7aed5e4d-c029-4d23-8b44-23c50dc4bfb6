#!/usr/bin/env python3
"""
Python OS - A Linux-like desktop environment for Windows
Main entry point for the operating system interface
"""

import tkinter as tk
from tkinter import ttk
import os
import sys
import json
from datetime import datetime

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ui.desktop import Desktop
from system.window_manager import WindowManager
from system.app_launcher import AppLauncher

class PythonOS:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_main_window()
        self.load_config()
        self.init_components()

    def setup_main_window(self):
        """Configure the main OS window"""
        self.root.title("Python OS")
        self.root.geometry("1024x768")
        self.root.configure(bg='#2c3e50')

        # Make it fullscreen-like but allow windowed mode for development
        self.root.state('zoomed')  # Maximize on Windows

        # Set window icon (if available)
        try:
            self.root.iconbitmap('resources/icons/os_icon.ico')
        except:
            pass

    def load_config(self):
        """Load OS configuration"""
        try:
            with open('config/settings.json', 'r') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            # Default configuration
            self.config = {
                "theme": "dark",
                "wallpaper": "#2c3e50",
                "taskbar_position": "bottom",
                "auto_hide_taskbar": False,
                "desktop_icons": True
            }
            self.save_config()

    def save_config(self):
        """Save OS configuration"""
        os.makedirs('config', exist_ok=True)
        with open('config/settings.json', 'w') as f:
            json.dump(self.config, f, indent=4)

    def init_components(self):
        """Initialize OS components"""
        # Initialize window manager
        self.window_manager = WindowManager(self.root)

        # Initialize app launcher
        self.app_launcher = AppLauncher(self.window_manager)

        # Initialize desktop environment
        self.desktop = Desktop(self.root, self.config, self.app_launcher)

        # Bind global shortcuts
        self.setup_shortcuts()

    def setup_shortcuts(self):
        """Setup global keyboard shortcuts"""
        self.root.bind('<Control-Alt-t>', lambda e: self.app_launcher.launch_app('terminal'))
        self.root.bind('<Control-Alt-f>', lambda e: self.app_launcher.launch_app('file_manager'))
        self.root.bind('<Control-Alt-b>', lambda e: self.app_launcher.launch_app('browser'))
        self.root.bind('<Control-Alt-m>', lambda e: self.app_launcher.launch_app('media_player'))
        self.root.bind('<Alt-F4>', lambda e: self.shutdown())

    def shutdown(self):
        """Shutdown the OS"""
        # Save any pending configuration
        self.save_config()

        # Close all applications
        self.window_manager.close_all_windows()

        # Exit
        self.root.quit()

    def run(self):
        """Start the OS"""
        print("Starting Python OS...")
        print("Press Ctrl+Alt+T for Terminal")
        print("Press Ctrl+Alt+F for File Manager")
        print("Press Ctrl+Alt+B for Browser")
        print("Press Ctrl+Alt+M for Media Player")
        print("Press Alt+F4 to exit")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.shutdown()

def main():
    """Main entry point"""
    # Create necessary directories
    os.makedirs('config', exist_ok=True)
    os.makedirs('resources/icons', exist_ok=True)
    os.makedirs('resources/themes', exist_ok=True)

    # Start the OS
    os_instance = PythonOS()
    os_instance.run()

if __name__ == "__main__":
    main()
