"""
Media Player Application - Audio and video player with playlist support
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import threading
import time
from datetime import timed<PERSON><PERSON>

from ui.window import CustomWindow

try:
    import pygame
    pygame.mixer.init()
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

class MediaPlayer:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id
        
        self.current_file = None
        self.playlist = []
        self.current_index = 0
        self.is_playing = False
        self.is_paused = False
        self.position = 0
        self.duration = 0
        self.volume = 70
        
        self.create_window()
        self.create_interface()
        
        if not PYGAME_AVAILABLE:
            self.show_pygame_warning()
            
    def create_window(self):
        """Create the media player window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="Media Player",
            width=800,
            height=600
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()
        
    def create_interface(self):
        """Create the media player interface"""
        # Menu bar
        self.create_menu()
        
        # Main content area
        main_frame = tk.Frame(self.content_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Left panel - Playlist
        left_frame = tk.Frame(main_frame, width=250)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.pack_propagate(False)
        
        self.create_playlist_panel(left_frame)
        
        # Right panel - Player controls and info
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_player_panel(right_frame)
        
    def create_menu(self):
        """Create menu bar"""
        menubar = tk.Menu(self.window)
        self.window.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Open File", command=self.open_file)
        file_menu.add_command(label="Open Folder", command=self.open_folder)
        file_menu.add_separator()
        file_menu.add_command(label="Save Playlist", command=self.save_playlist)
        file_menu.add_command(label="Load Playlist", command=self.load_playlist)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.window.destroy)
        
        # Playback menu
        playback_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Playback", menu=playback_menu)
        playback_menu.add_command(label="Play/Pause", command=self.toggle_playback)
        playback_menu.add_command(label="Stop", command=self.stop)
        playback_menu.add_command(label="Previous", command=self.previous_track)
        playback_menu.add_command(label="Next", command=self.next_track)
        playback_menu.add_separator()
        playback_menu.add_command(label="Shuffle", command=self.toggle_shuffle)
        playback_menu.add_command(label="Repeat", command=self.toggle_repeat)
        
    def create_playlist_panel(self, parent):
        """Create playlist panel"""
        # Playlist header
        playlist_header = tk.Frame(parent)
        playlist_header.pack(fill=tk.X, pady=(0, 5))
        
        tk.Label(playlist_header, text="Playlist", font=("Arial", 12, "bold")).pack(side=tk.LEFT)
        
        # Playlist controls
        playlist_controls = tk.Frame(playlist_header)
        playlist_controls.pack(side=tk.RIGHT)
        
        tk.Button(playlist_controls, text="+", command=self.add_files, width=3).pack(side=tk.LEFT, padx=1)
        tk.Button(playlist_controls, text="-", command=self.remove_selected, width=3).pack(side=tk.LEFT, padx=1)
        tk.Button(playlist_controls, text="↑", command=self.move_up, width=3).pack(side=tk.LEFT, padx=1)
        tk.Button(playlist_controls, text="↓", command=self.move_down, width=3).pack(side=tk.LEFT, padx=1)
        
        # Playlist listbox
        playlist_frame = tk.Frame(parent)
        playlist_frame.pack(fill=tk.BOTH, expand=True)
        
        self.playlist_listbox = tk.Listbox(playlist_frame, selectmode=tk.SINGLE)
        playlist_scrollbar = ttk.Scrollbar(playlist_frame, orient=tk.VERTICAL, command=self.playlist_listbox.yview)
        self.playlist_listbox.configure(yscrollcommand=playlist_scrollbar.set)
        
        self.playlist_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        playlist_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Bind double-click to play
        self.playlist_listbox.bind('<Double-1>', self.play_selected)
        
        # Playlist info
        self.playlist_info = tk.Label(parent, text="0 tracks", font=("Arial", 9))
        self.playlist_info.pack(pady=(5, 0))
        
    def create_player_panel(self, parent):
        """Create player control panel"""
        # Now playing info
        info_frame = tk.LabelFrame(parent, text="Now Playing", padx=10, pady=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.track_title = tk.Label(info_frame, text="No track selected", font=("Arial", 14, "bold"))
        self.track_title.pack()
        
        self.track_info = tk.Label(info_frame, text="", font=("Arial", 10))
        self.track_info.pack()
        
        # Progress bar
        progress_frame = tk.Frame(parent)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.time_label = tk.Label(progress_frame, text="00:00", font=("Arial", 10))
        self.time_label.pack(side=tk.LEFT)
        
        self.progress_var = tk.DoubleVar()
        self.progress_scale = tk.Scale(
            progress_frame,
            from_=0,
            to=100,
            orient=tk.HORIZONTAL,
            variable=self.progress_var,
            showvalue=0,
            command=self.seek
        )
        self.progress_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)
        
        self.duration_label = tk.Label(progress_frame, text="00:00", font=("Arial", 10))
        self.duration_label.pack(side=tk.RIGHT)
        
        # Control buttons
        controls_frame = tk.Frame(parent)
        controls_frame.pack(pady=10)
        
        # Main playback controls
        main_controls = tk.Frame(controls_frame)
        main_controls.pack()
        
        self.prev_btn = tk.Button(
            main_controls,
            text="⏮",
            command=self.previous_track,
            font=("Arial", 16),
            width=4,
            height=2
        )
        self.prev_btn.pack(side=tk.LEFT, padx=5)
        
        self.play_pause_btn = tk.Button(
            main_controls,
            text="▶",
            command=self.toggle_playback,
            font=("Arial", 20),
            width=4,
            height=2,
            bg='#27ae60',
            fg='white'
        )
        self.play_pause_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = tk.Button(
            main_controls,
            text="⏹",
            command=self.stop,
            font=("Arial", 16),
            width=4,
            height=2
        )
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        self.next_btn = tk.Button(
            main_controls,
            text="⏭",
            command=self.next_track,
            font=("Arial", 16),
            width=4,
            height=2
        )
        self.next_btn.pack(side=tk.LEFT, padx=5)
        
        # Secondary controls
        secondary_controls = tk.Frame(controls_frame)
        secondary_controls.pack(pady=(10, 0))
        
        # Volume control
        volume_frame = tk.Frame(secondary_controls)
        volume_frame.pack(side=tk.LEFT, padx=20)
        
        tk.Label(volume_frame, text="🔊", font=("Arial", 12)).pack(side=tk.LEFT)
        
        self.volume_var = tk.IntVar(value=self.volume)
        self.volume_scale = tk.Scale(
            volume_frame,
            from_=0,
            to=100,
            orient=tk.HORIZONTAL,
            variable=self.volume_var,
            command=self.set_volume,
            length=150
        )
        self.volume_scale.pack(side=tk.LEFT, padx=5)
        
        # Mode buttons
        mode_frame = tk.Frame(secondary_controls)
        mode_frame.pack(side=tk.RIGHT, padx=20)
        
        self.shuffle_var = tk.BooleanVar()
        self.shuffle_btn = tk.Checkbutton(
            mode_frame,
            text="🔀",
            variable=self.shuffle_var,
            font=("Arial", 12),
            command=self.toggle_shuffle
        )
        self.shuffle_btn.pack(side=tk.LEFT, padx=5)
        
        self.repeat_var = tk.BooleanVar()
        self.repeat_btn = tk.Checkbutton(
            mode_frame,
            text="🔁",
            variable=self.repeat_var,
            font=("Arial", 12),
            command=self.toggle_repeat
        )
        self.repeat_btn.pack(side=tk.LEFT, padx=5)
        
        # Visualizer placeholder
        visualizer_frame = tk.LabelFrame(parent, text="Visualizer", padx=10, pady=10)
        visualizer_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        self.visualizer_canvas = tk.Canvas(
            visualizer_frame,
            bg='black',
            height=150
        )
        self.visualizer_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Add some visual elements
        self.create_visualizer_elements()
        
    def create_visualizer_elements(self):
        """Create basic visualizer elements"""
        # Simple bars for visualization
        self.visualizer_bars = []
        canvas_width = 400  # Approximate width
        bar_count = 20
        bar_width = canvas_width // bar_count
        
        for i in range(bar_count):
            x = i * bar_width
            bar = self.visualizer_canvas.create_rectangle(
                x, 150, x + bar_width - 2, 150,
                fill='#3498db',
                outline=''
            )
            self.visualizer_bars.append(bar)
            
    def show_pygame_warning(self):
        """Show warning if pygame is not available"""
        warning_frame = tk.Frame(self.content_frame, bg='#f39c12')
        warning_frame.pack(fill=tk.X, padx=10, pady=5)
        
        tk.Label(
            warning_frame,
            text="⚠️ Pygame not installed. Audio playback will be limited.",
            bg='#f39c12',
            fg='white',
            font=("Arial", 10, "bold")
        ).pack(pady=5)
        
    def open_file(self):
        """Open audio file"""
        filetypes = [
            ("Audio files", "*.mp3 *.wav *.ogg *.m4a"),
            ("MP3 files", "*.mp3"),
            ("WAV files", "*.wav"),
            ("All files", "*.*")
        ]
        
        filename = filedialog.askopenfilename(
            title="Open Audio File",
            filetypes=filetypes
        )
        
        if filename:
            self.add_to_playlist(filename)
            
    def open_folder(self):
        """Open folder and add all audio files"""
        folder = filedialog.askdirectory(title="Select Music Folder")
        
        if folder:
            audio_extensions = ['.mp3', '.wav', '.ogg', '.m4a', '.flac']
            files_added = 0
            
            for root, dirs, files in os.walk(folder):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in audio_extensions):
                        filepath = os.path.join(root, file)
                        self.add_to_playlist(filepath)
                        files_added += 1
                        
            messagebox.showinfo("Success", f"Added {files_added} audio files to playlist")
            
    def add_to_playlist(self, filepath):
        """Add file to playlist"""
        if filepath not in self.playlist:
            self.playlist.append(filepath)
            filename = os.path.basename(filepath)
            self.playlist_listbox.insert(tk.END, filename)
            self.update_playlist_info()
            
    def add_files(self):
        """Add multiple files to playlist"""
        filetypes = [
            ("Audio files", "*.mp3 *.wav *.ogg *.m4a"),
            ("All files", "*.*")
        ]
        
        filenames = filedialog.askopenfilenames(
            title="Add Audio Files",
            filetypes=filetypes
        )
        
        for filename in filenames:
            self.add_to_playlist(filename)
            
    def remove_selected(self):
        """Remove selected track from playlist"""
        selection = self.playlist_listbox.curselection()
        if selection:
            index = selection[0]
            self.playlist_listbox.delete(index)
            del self.playlist[index]
            
            # Adjust current index if necessary
            if index <= self.current_index:
                self.current_index = max(0, self.current_index - 1)
                
            self.update_playlist_info()
            
    def move_up(self):
        """Move selected track up in playlist"""
        selection = self.playlist_listbox.curselection()
        if selection and selection[0] > 0:
            index = selection[0]
            
            # Swap in playlist
            self.playlist[index], self.playlist[index-1] = self.playlist[index-1], self.playlist[index]
            
            # Update listbox
            self.playlist_listbox.delete(index)
            self.playlist_listbox.insert(index-1, os.path.basename(self.playlist[index-1]))
            self.playlist_listbox.selection_set(index-1)
            
    def move_down(self):
        """Move selected track down in playlist"""
        selection = self.playlist_listbox.curselection()
        if selection and selection[0] < len(self.playlist) - 1:
            index = selection[0]
            
            # Swap in playlist
            self.playlist[index], self.playlist[index+1] = self.playlist[index+1], self.playlist[index]
            
            # Update listbox
            self.playlist_listbox.delete(index)
            self.playlist_listbox.insert(index+1, os.path.basename(self.playlist[index+1]))
            self.playlist_listbox.selection_set(index+1)
            
    def play_selected(self, event=None):
        """Play selected track"""
        selection = self.playlist_listbox.curselection()
        if selection:
            self.current_index = selection[0]
            self.play_current_track()
            
    def play_current_track(self):
        """Play current track"""
        if not self.playlist or self.current_index >= len(self.playlist):
            return
            
        self.current_file = self.playlist[self.current_index]
        filename = os.path.basename(self.current_file)
        
        self.track_title.config(text=filename)
        self.track_info.config(text=f"Track {self.current_index + 1} of {len(self.playlist)}")
        
        # Highlight current track in playlist
        self.playlist_listbox.selection_clear(0, tk.END)
        self.playlist_listbox.selection_set(self.current_index)
        self.playlist_listbox.see(self.current_index)
        
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.music.load(self.current_file)
                pygame.mixer.music.play()
                self.is_playing = True
                self.is_paused = False
                self.play_pause_btn.config(text="⏸", bg='#e74c3c')
                
                # Start position tracking
                self.start_position_tracking()
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to play file: {e}")
        else:
            messagebox.showinfo("Info", f"Would play: {filename}\n(Pygame not available)")
            
    def toggle_playback(self):
        """Toggle play/pause"""
        if not PYGAME_AVAILABLE:
            messagebox.showinfo("Info", "Pygame not available for audio playback")
            return
            
        if not self.current_file:
            if self.playlist:
                self.current_index = 0
                self.play_current_track()
            return
            
        if self.is_playing:
            if self.is_paused:
                pygame.mixer.music.unpause()
                self.is_paused = False
                self.play_pause_btn.config(text="⏸", bg='#e74c3c')
            else:
                pygame.mixer.music.pause()
                self.is_paused = True
                self.play_pause_btn.config(text="▶", bg='#27ae60')
        else:
            self.play_current_track()
            
    def stop(self):
        """Stop playback"""
        if PYGAME_AVAILABLE:
            pygame.mixer.music.stop()
            
        self.is_playing = False
        self.is_paused = False
        self.position = 0
        self.play_pause_btn.config(text="▶", bg='#27ae60')
        self.progress_var.set(0)
        self.time_label.config(text="00:00")
        
    def previous_track(self):
        """Play previous track"""
        if self.playlist:
            self.current_index = (self.current_index - 1) % len(self.playlist)
            self.play_current_track()
            
    def next_track(self):
        """Play next track"""
        if self.playlist:
            if self.shuffle_var.get():
                import random
                self.current_index = random.randint(0, len(self.playlist) - 1)
            else:
                self.current_index = (self.current_index + 1) % len(self.playlist)
            self.play_current_track()
            
    def seek(self, value):
        """Seek to position"""
        # Note: pygame doesn't support seeking, this is a placeholder
        pass
        
    def set_volume(self, value):
        """Set volume"""
        self.volume = int(value)
        if PYGAME_AVAILABLE:
            pygame.mixer.music.set_volume(self.volume / 100.0)
            
    def toggle_shuffle(self):
        """Toggle shuffle mode"""
        # Shuffle functionality is implemented in next_track
        pass
        
    def toggle_repeat(self):
        """Toggle repeat mode"""
        # Repeat functionality would be implemented in playback completion
        pass
        
    def start_position_tracking(self):
        """Start tracking playback position"""
        def track_position():
            while self.is_playing and not self.is_paused:
                if PYGAME_AVAILABLE and pygame.mixer.music.get_busy():
                    # Pygame doesn't provide position info, so we'll simulate it
                    self.position += 1
                    
                    # Update time display
                    time_str = str(timedelta(seconds=self.position))
                    if time_str.startswith('0:'):
                        time_str = time_str[2:]
                    self.time_label.config(text=time_str)
                    
                    # Update visualizer
                    self.update_visualizer()
                    
                    time.sleep(1)
                else:
                    # Track finished
                    if self.repeat_var.get():
                        self.play_current_track()
                    else:
                        self.next_track()
                    break
                    
        if self.is_playing:
            threading.Thread(target=track_position, daemon=True).start()
            
    def update_visualizer(self):
        """Update visualizer bars"""
        import random
        
        for bar in self.visualizer_bars:
            # Simulate audio levels with random heights
            height = random.randint(10, 140) if self.is_playing and not self.is_paused else 0
            
            # Get current coordinates
            coords = self.visualizer_canvas.coords(bar)
            if coords:
                x1, y1, x2, y2 = coords
                # Update bar height
                self.visualizer_canvas.coords(bar, x1, 150 - height, x2, 150)
                
    def update_playlist_info(self):
        """Update playlist information"""
        count = len(self.playlist)
        self.playlist_info.config(text=f"{count} track{'s' if count != 1 else ''}")
        
    def save_playlist(self):
        """Save playlist to file"""
        if not self.playlist:
            messagebox.showwarning("Warning", "Playlist is empty")
            return
            
        filename = filedialog.asksaveasfilename(
            title="Save Playlist",
            defaultextension=".m3u",
            filetypes=[("M3U Playlist", "*.m3u"), ("Text files", "*.txt")]
        )
        
        if filename:
            try:
                with open(filename, 'w') as f:
                    for track in self.playlist:
                        f.write(f"{track}\n")
                messagebox.showinfo("Success", "Playlist saved successfully")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save playlist: {e}")
                
    def load_playlist(self):
        """Load playlist from file"""
        filename = filedialog.askopenfilename(
            title="Load Playlist",
            filetypes=[("M3U Playlist", "*.m3u"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r') as f:
                    tracks = [line.strip() for line in f if line.strip()]
                    
                # Clear current playlist
                self.playlist.clear()
                self.playlist_listbox.delete(0, tk.END)
                
                # Add tracks
                for track in tracks:
                    if os.path.exists(track):
                        self.add_to_playlist(track)
                        
                messagebox.showinfo("Success", f"Loaded {len(self.playlist)} tracks")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load playlist: {e}")
                
    def cleanup(self):
        """Cleanup when closing"""
        if PYGAME_AVAILABLE:
            pygame.mixer.music.stop()
        self.is_playing = False
