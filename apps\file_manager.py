"""
File Manager Application - Browse and manage files and directories
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import os
import shutil
import subprocess
import platform
from datetime import datetime

from ui.window import CustomWindow

class FileManager:
    def __init__(self, parent, window_manager, window_id):
        self.parent = parent
        self.window_manager = window_manager
        self.window_id = window_id

        self.current_path = os.path.expanduser("~")  # Start in home directory
        self.history = [self.current_path]
        self.history_index = 0

        self.create_window()
        self.create_interface()
        self.refresh_view()

    def create_window(self):
        """Create the file manager window"""
        self.custom_window = CustomWindow(
            self.parent,
            title="File Manager",
            width=900,
            height=600
        )
        self.window = self.custom_window.get_window()
        self.content_frame = self.custom_window.get_content_frame()

    def create_interface(self):
        """Create the file manager interface"""
        # Toolbar
        self.create_toolbar()

        # Main content area
        main_frame = tk.Frame(self.content_frame, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Directory tree (simplified)
        left_frame = tk.Frame(main_frame, bg='#f8f9fa', width=200)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))
        left_frame.pack_propagate(False)

        tk.Label(left_frame, text="Quick Access", font=("Arial", 10, "bold"), bg='#f8f9fa').pack(pady=5)

        # Quick access buttons
        quick_paths = [
            ("Home", os.path.expanduser("~")),
            ("Desktop", os.path.join(os.path.expanduser("~"), "Desktop")),
            ("Documents", os.path.join(os.path.expanduser("~"), "Documents")),
            ("Downloads", os.path.join(os.path.expanduser("~"), "Downloads")),
        ]

        for name, path in quick_paths:
            if os.path.exists(path):
                btn = tk.Button(
                    left_frame,
                    text=name,
                    command=lambda p=path: self.navigate_to(p),
                    bg='white',
                    relief=tk.FLAT,
                    anchor='w',
                    padx=10
                )
                btn.pack(fill=tk.X, padx=5, pady=1)

        # Right panel - File list
        right_frame = tk.Frame(main_frame, bg='white')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # File list with scrollbar
        list_frame = tk.Frame(right_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # Create treeview for file list
        columns = ('Name', 'Size', 'Type', 'Modified')
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show='tree headings')

        # Configure columns
        self.file_tree.heading('#0', text='')
        self.file_tree.column('#0', width=20)

        for col in columns:
            self.file_tree.heading(col, text=col)
            if col == 'Name':
                self.file_tree.column(col, width=300)
            elif col == 'Size':
                self.file_tree.column(col, width=100)
            elif col == 'Type':
                self.file_tree.column(col, width=100)
            else:
                self.file_tree.column(col, width=150)

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.file_tree.xview)
        self.file_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.file_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Bind events
        self.file_tree.bind('<Double-1>', self.on_double_click)
        self.file_tree.bind('<Button-3>', self.show_context_menu)

        # Status bar
        self.status_bar = tk.Label(
            self.content_frame,
            text="Ready",
            relief=tk.SUNKEN,
            anchor=tk.W,
            bg='#f8f9fa'
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # Create context menu
        self.create_context_menu()

    def create_toolbar(self):
        """Create the toolbar"""
        toolbar = tk.Frame(self.content_frame, bg='#e9ecef', height=40)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        toolbar.pack_propagate(False)

        # Navigation buttons
        tk.Button(toolbar, text="←", command=self.go_back, width=3).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="→", command=self.go_forward, width=3).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="↑", command=self.go_up, width=3).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="🏠", command=self.go_home, width=3).pack(side=tk.LEFT, padx=2)
        tk.Button(toolbar, text="🔄", command=self.refresh_view, width=3).pack(side=tk.LEFT, padx=2)

        # Address bar
        tk.Label(toolbar, text="Path:", bg='#e9ecef').pack(side=tk.LEFT, padx=(10, 5))
        self.address_var = tk.StringVar(value=self.current_path)
        self.address_entry = tk.Entry(toolbar, textvariable=self.address_var, width=50)
        self.address_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
        self.address_entry.bind('<Return>', self.navigate_to_address)

        # Action buttons
        tk.Button(toolbar, text="New Folder", command=self.create_folder).pack(side=tk.RIGHT, padx=2)

    def create_context_menu(self):
        """Create context menu for files"""
        self.context_menu = tk.Menu(self.window, tearoff=0)
        self.context_menu.add_command(label="Open", command=self.open_selected)
        self.context_menu.add_command(label="Open with...", command=self.open_with)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Copy", command=self.copy_selected)
        self.context_menu.add_command(label="Cut", command=self.cut_selected)
        self.context_menu.add_command(label="Paste", command=self.paste_here)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Rename", command=self.rename_selected)
        self.context_menu.add_command(label="Delete", command=self.delete_selected)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Properties", command=self.show_properties)

    def refresh_view(self):
        """Refresh the file list"""
        # Clear current items
        for item in self.file_tree.get_children():
            self.file_tree.delete(item)

        try:
            # Update address bar
            self.address_var.set(self.current_path)

            # Get directory contents
            items = []

            # Add parent directory if not at root
            if self.current_path != os.path.dirname(self.current_path):
                items.append(('..',  'folder', '', ''))

            # Get files and folders
            try:
                for item in os.listdir(self.current_path):
                    item_path = os.path.join(self.current_path, item)

                    if os.path.isdir(item_path):
                        items.append((item, 'folder', '', self.get_modified_time(item_path)))
                    else:
                        size = self.format_size(os.path.getsize(item_path))
                        ext = os.path.splitext(item)[1] or 'File'
                        items.append((item, 'file', size, self.get_modified_time(item_path)))

            except PermissionError:
                messagebox.showerror("Error", "Permission denied")
                return

            # Sort items (folders first, then files)
            items.sort(key=lambda x: (x[1] != 'folder', x[0].lower()))

            # Add items to tree
            for name, item_type, size, modified in items:
                icon = '📁' if item_type == 'folder' else '📄'
                self.file_tree.insert('', 'end', text=icon, values=(name, size, item_type, modified))

            # Update status
            file_count = sum(1 for _, t, _, _ in items if t == 'file')
            folder_count = sum(1 for _, t, _, _ in items if t == 'folder')
            self.status_bar.config(text=f"{folder_count} folders, {file_count} files")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to refresh view: {e}")

    def navigate_to(self, path):
        """Navigate to a specific path"""
        if os.path.exists(path) and os.path.isdir(path):
            self.current_path = os.path.abspath(path)

            # Update history
            if self.history_index < len(self.history) - 1:
                self.history = self.history[:self.history_index + 1]
            self.history.append(self.current_path)
            self.history_index = len(self.history) - 1

            self.refresh_view()
        else:
            messagebox.showerror("Error", f"Path does not exist: {path}")

    def navigate_to_address(self, event=None):
        """Navigate to address bar path"""
        path = self.address_var.get()
        self.navigate_to(path)

    def on_double_click(self, event):
        """Handle double-click on file/folder"""
        selection = self.file_tree.selection()
        if selection:
            item = self.file_tree.item(selection[0])
            name = item['values'][0]

            if name == '..':
                self.go_up()
            else:
                item_path = os.path.join(self.current_path, name)
                if os.path.isdir(item_path):
                    self.navigate_to(item_path)
                else:
                    self.open_file(item_path)

    def open_file(self, file_path):
        """Open a file with default application"""
        try:
            if platform.system() == 'Windows':
                os.startfile(file_path)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', file_path])
            else:  # Linux
                subprocess.run(['xdg-open', file_path])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open file: {e}")

    def go_back(self):
        """Go back in history"""
        if self.history_index > 0:
            self.history_index -= 1
            self.current_path = self.history[self.history_index]
            self.refresh_view()

    def go_forward(self):
        """Go forward in history"""
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            self.current_path = self.history[self.history_index]
            self.refresh_view()

    def go_up(self):
        """Go to parent directory"""
        parent = os.path.dirname(self.current_path)
        if parent != self.current_path:
            self.navigate_to(parent)

    def go_home(self):
        """Go to home directory"""
        self.navigate_to(os.path.expanduser("~"))

    def format_size(self, size):
        """Format file size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"

    def get_modified_time(self, path):
        """Get formatted modification time"""
        try:
            mtime = os.path.getmtime(path)
            return datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M")
        except:
            return ""

    def show_context_menu(self, event):
        """Show context menu"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()

    def open_selected(self):
        """Open selected file/folder"""
        selection = self.file_tree.selection()
        if selection:
            item = self.file_tree.item(selection[0])
            name = item['values'][0]
            item_path = os.path.join(self.current_path, name)

            if os.path.isdir(item_path):
                self.navigate_to(item_path)
            else:
                self.open_file(item_path)

    def open_with(self):
        """Open file with specific application"""
        messagebox.showinfo("Info", "Open with functionality not implemented yet")

    def copy_selected(self):
        """Copy selected file/folder"""
        messagebox.showinfo("Info", "Copy functionality not implemented yet")

    def cut_selected(self):
        """Cut selected file/folder"""
        messagebox.showinfo("Info", "Cut functionality not implemented yet")

    def paste_here(self):
        """Paste file/folder here"""
        messagebox.showinfo("Info", "Paste functionality not implemented yet")

    def rename_selected(self):
        """Rename selected file/folder"""
        messagebox.showinfo("Info", "Rename functionality not implemented yet")

    def delete_selected(self):
        """Delete selected file/folder"""
        selection = self.file_tree.selection()
        if selection:
            item = self.file_tree.item(selection[0])
            name = item['values'][0]

            if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{name}'?"):
                try:
                    item_path = os.path.join(self.current_path, name)
                    if os.path.isdir(item_path):
                        shutil.rmtree(item_path)
                    else:
                        os.remove(item_path)
                    self.refresh_view()
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to delete: {e}")

    def show_properties(self):
        """Show file/folder properties"""
        messagebox.showinfo("Info", "Properties functionality not implemented yet")

    def create_folder(self):
        """Create new folder"""
        name = tk.simpledialog.askstring("New Folder", "Enter folder name:")
        if name:
            try:
                folder_path = os.path.join(self.current_path, name)
                os.makedirs(folder_path)
                self.refresh_view()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to create folder: {e}")

    def cleanup(self):
        """Cleanup when closing"""
        pass
