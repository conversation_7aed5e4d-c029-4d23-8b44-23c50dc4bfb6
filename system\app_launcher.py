"""
Application Launcher - Launches and manages applications
"""

import tkinter as tk
from tkinter import messagebox
import uuid
import importlib
import sys
import os

class AppLauncher:
    def __init__(self, window_manager):
        self.window_manager = window_manager
        self.running_apps = {}
        
        # Define available applications
        self.available_apps = {
            'file_manager': {
                'name': 'File Manager',
                'module': 'apps.file_manager',
                'class': 'FileManager',
                'icon': '📁'
            },
            'browser': {
                'name': 'Web Browser',
                'module': 'apps.browser',
                'class': 'WebBrowser',
                'icon': '🌐'
            },
            'terminal': {
                'name': 'Terminal',
                'module': 'apps.terminal',
                'class': 'Terminal',
                'icon': '💻'
            },
            'text_editor': {
                'name': 'Text Editor',
                'module': 'apps.text_editor',
                'class': 'TextEditor',
                'icon': '📝'
            },
            'settings': {
                'name': 'Settings',
                'module': 'apps.settings',
                'class': 'Settings',
                'icon': '⚙️'
            }
        }
        
    def launch_app(self, app_id: str, *args, **kwargs):
        """Launch an application"""
        if app_id not in self.available_apps:
            messagebox.showerror("Error", f"Application '{app_id}' not found")
            return None
            
        app_info = self.available_apps[app_id]
        
        try:
            # Generate unique window ID
            window_id = f"{app_id}_{uuid.uuid4().hex[:8]}"
            
            # Import the application module
            module = importlib.import_module(app_info['module'])
            app_class = getattr(module, app_info['class'])
            
            # Create application instance
            app_instance = app_class(
                parent=self.window_manager.root,
                window_manager=self.window_manager,
                window_id=window_id,
                *args,
                **kwargs
            )
            
            # Register with running apps
            self.running_apps[window_id] = {
                'app_id': app_id,
                'instance': app_instance,
                'name': app_info['name']
            }
            
            # Register window with window manager
            if hasattr(app_instance, 'window'):
                self.window_manager.register_window(
                    window_id,
                    app_instance.window,
                    app_info['name']
                )
                
                # Bind close event to cleanup
                original_close = app_instance.window.protocol("WM_DELETE_WINDOW")
                app_instance.window.protocol(
                    "WM_DELETE_WINDOW",
                    lambda: self.close_app(window_id)
                )
            
            print(f"Launched {app_info['name']} (ID: {window_id})")
            return app_instance
            
        except ImportError as e:
            messagebox.showerror(
                "Error",
                f"Could not load application '{app_info['name']}':\n{e}\n\nThe application module may not be implemented yet."
            )
            return None
        except Exception as e:
            messagebox.showerror(
                "Error",
                f"Failed to launch '{app_info['name']}':\n{e}"
            )
            return None
            
    def close_app(self, window_id: str):
        """Close an application"""
        if window_id in self.running_apps:
            app_info = self.running_apps[window_id]
            
            try:
                # Call app's cleanup method if it exists
                if hasattr(app_info['instance'], 'cleanup'):
                    app_info['instance'].cleanup()
                    
                # Destroy the window
                if hasattr(app_info['instance'], 'window'):
                    app_info['instance'].window.destroy()
                    
            except Exception as e:
                print(f"Error closing app {window_id}: {e}")
                
            # Remove from running apps
            del self.running_apps[window_id]
            
            # Unregister from window manager
            self.window_manager.unregister_window(window_id)
            
            print(f"Closed {app_info['name']} (ID: {window_id})")
            
    def get_running_apps(self):
        """Get list of running applications"""
        return [
            {
                'window_id': window_id,
                'app_id': info['app_id'],
                'name': info['name']
            }
            for window_id, info in self.running_apps.items()
        ]
        
    def get_available_apps(self):
        """Get list of available applications"""
        return self.available_apps
        
    def is_app_running(self, app_id: str) -> bool:
        """Check if an application is currently running"""
        return any(
            info['app_id'] == app_id
            for info in self.running_apps.values()
        )
        
    def focus_app(self, window_id: str):
        """Focus on a running application"""
        if window_id in self.running_apps:
            self.window_manager.focus_window(window_id)
            
    def close_all_apps(self):
        """Close all running applications"""
        window_ids = list(self.running_apps.keys())
        for window_id in window_ids:
            self.close_app(window_id)
            
    def restart_app(self, window_id: str):
        """Restart an application"""
        if window_id in self.running_apps:
            app_id = self.running_apps[window_id]['app_id']
            self.close_app(window_id)
            self.launch_app(app_id)
            
    def launch_external_app(self, command: str):
        """Launch an external application"""
        try:
            import subprocess
            subprocess.Popen(command, shell=True)
            print(f"Launched external command: {command}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch external application:\n{e}")
            
    def register_app(self, app_id: str, app_info: dict):
        """Register a new application"""
        self.available_apps[app_id] = app_info
        print(f"Registered application: {app_info['name']}")
        
    def unregister_app(self, app_id: str):
        """Unregister an application"""
        if app_id in self.available_apps:
            app_name = self.available_apps[app_id]['name']
            del self.available_apps[app_id]
            print(f"Unregistered application: {app_name}")
            
    def get_app_info(self, app_id: str):
        """Get information about an application"""
        return self.available_apps.get(app_id)
